'use client'

import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Calendar, ChevronLeft, ChevronRight, MapPin } from 'lucide-react'

interface DepartureDateCalendarProps {
  availableDates: string[] // Array of dates in YYYY-MM-DD format
  onDateSelect?: (date: string) => void
  selectedDate?: string
  className?: string
}

interface CalendarDay {
  date: Date
  dateString: string
  isCurrentMonth: boolean
  isAvailable: boolean
  isSelected: boolean
  isToday: boolean
}

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]

const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

export default function DepartureDateCalendar({
  availableDates = [],
  onDateSelect,
  selectedDate,
  className = ''
}: DepartureDateCalendarProps) {
  // Start with current date, but don't allow going to past months
  const today = new Date()
  const [currentDate, setCurrentDate] = useState(new Date(today.getFullYear(), today.getMonth(), 1))

  // Convert available dates to Set for faster lookup
  const availableDatesSet = useMemo(() => new Set(availableDates), [availableDates])

  // Get current month and year
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()

  // Check if we can go to previous month (don't allow past months)
  const canGoPrevious = currentYear > today.getFullYear() ||
    (currentYear === today.getFullYear() && currentMonth > today.getMonth())
  
  // Generate calendar days for current month
  const calendarDays = useMemo(() => {
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
    const firstDayOfWeek = firstDayOfMonth.getDay()
    const daysInMonth = lastDayOfMonth.getDate()
    
    const days: CalendarDay[] = []
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    // Helper function to format date as YYYY-MM-DD without timezone issues
    const formatDateString = (date: Date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // Add previous month's trailing days
    const prevMonth = new Date(currentYear, currentMonth - 1, 0)
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(currentYear, currentMonth - 1, prevMonth.getDate() - i)
      const dateString = formatDateString(date)
      days.push({
        date,
        dateString,
        isCurrentMonth: false,
        isAvailable: availableDatesSet.has(dateString),
        isSelected: dateString === selectedDate,
        isToday: date.getTime() === today.getTime()
      })
    }

    // Add current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth, day)
      const dateString = formatDateString(date)
      days.push({
        date,
        dateString,
        isCurrentMonth: true,
        isAvailable: availableDatesSet.has(dateString),
        isSelected: dateString === selectedDate,
        isToday: date.getTime() === today.getTime()
      })
    }

    // Add next month's leading days to complete the grid
    const remainingDays = 42 - days.length // 6 rows × 7 days
    for (let day = 1; day <= remainingDays; day++) {
      const date = new Date(currentYear, currentMonth + 1, day)
      const dateString = formatDateString(date)
      days.push({
        date,
        dateString,
        isCurrentMonth: false,
        isAvailable: availableDatesSet.has(dateString),
        isSelected: dateString === selectedDate,
        isToday: date.getTime() === today.getTime()
      })
    }
    
    return days
  }, [currentMonth, currentYear, availableDatesSet, selectedDate])
  
  // Navigate to previous month (only if not in the past)
  const goToPreviousMonth = () => {
    if (canGoPrevious) {
      setCurrentDate(new Date(currentYear, currentMonth - 1, 1))
    }
  }

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1))
  }

  // Handle date selection - disabled for display-only calendar
  const handleDateClick = (day: CalendarDay) => {
    // Calendar is now display-only, no clicking allowed
    return
  }
  
  // Format date for display
  const formatDateForDisplay = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }
  
  return (
    <div className={`bg-white rounded-2xl p-6 shadow-xl ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <Calendar className="w-6 h-6 text-blue-600" />
          Available Departure Dates
        </h3>
        <div className="text-sm text-gray-600">
          {availableDates.length} dates available
        </div>
      </div>
      
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={goToPreviousMonth}
          disabled={!canGoPrevious}
          className={`p-2 rounded-lg transition-colors ${
            canGoPrevious
              ? 'hover:bg-gray-100 text-gray-600'
              : 'text-gray-300 cursor-not-allowed'
          }`}
          aria-label="Previous month"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>
        
        <h4 className="text-lg font-semibold text-gray-900">
          {MONTHS[currentMonth]} {currentYear}
        </h4>
        
        <button
          onClick={goToNextMonth}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          aria-label="Next month"
        >
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
      </div>
      
      {/* Days of week header */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {DAYS.map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
            {day}
          </div>
        ))}
      </div>
      
      {/* Calendar grid - Display only, no interactions */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((day, index) => (
          <div
            key={`${day.dateString}-${index}`}
            className={`
              p-2 text-sm rounded-lg transition-all duration-200 relative
              ${day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
              ${day.isAvailable
                ? 'bg-blue-500 text-blue-700 border border-blue-500'
                : 'bg-gray-50'
              }
              ${day.isToday ? 'ring-2 ring-blue-300' : ''}
            `}
          >
            {day.date.getDate()}
          </div>
        ))}
      </div>
      
      {/* No dates available message */}
      {availableDates.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-sm">No departure dates available at the moment.</p>
          <p className="text-xs mt-1">Please check back later or contact us for more information.</p>
        </div>
      )}
    </div>
  )
}
