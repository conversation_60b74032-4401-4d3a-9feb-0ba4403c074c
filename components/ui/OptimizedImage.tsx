'use client';

import { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { getImageWithFallback } from '@/lib/image-fallbacks';

interface OptimizedImageProps extends Omit<ImageProps, 'src'> {
  src: string | null | undefined;
  fallbackSrc?: string;
  optimizeQuality?: number;
}

/**
 * OptimizedImage component that wraps Next.js Image component with proper
 * error handling, fallbacks, and optimization.
 */
export default function OptimizedImage({
  src,
  fallbackSrc = '/images/fallback-image.jpg',
  alt,
  width,
  height,
  optimizeQuality = 80,
  priority = false,
  className = '',
  style,
  ...props
}: OptimizedImageProps) {
  const [error, setError] = useState(false);
  
  // Process the source URL
  const processedSrc = error 
    ? fallbackSrc 
    : getImageWithFallback(src, typeof width === 'number' ? width : undefined, optimizeQuality);
  
  return (
    <Image
      src={processedSrc}
      alt={alt || 'Image'}
      width={width}
      height={height}
      priority={priority}
      className={`${className} ${error ? 'opacity-80' : ''}`}
      style={style}
      onError={() => setError(true)}
      {...props}
    />
  );
}

/**
 * This component is used for images from external domains
 * where the exact dimensions aren't known.
 */
export function ResponsiveImage({
  src,
  alt,
  fallbackSrc = '/images/fallback-image.jpg',
  aspectRatio = '16/9',
  className = '',
  ...props
}: Omit<OptimizedImageProps, 'width' | 'height'> & {
  aspectRatio?: string;
}) {
  const [error, setError] = useState(false);
  
  // Process the source URL (width will be set by CSS)
  const processedSrc = error 
    ? fallbackSrc 
    : getImageWithFallback(src, 1200, 80);
  
  return (
    <div 
      className={`relative w-full overflow-hidden ${className}`} 
      style={{ aspectRatio }}
    >
      <Image
        src={processedSrc}
        alt={alt || 'Image'}
        fill
        className={`object-cover ${error ? 'opacity-80' : ''}`}
        onError={() => setError(true)}
        {...props}
      />
    </div>
  );
} 