'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/layout/AdminLayout';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import AdminLoadingSpinner, { AdminLoadingPresets } from '@/components/ui/AdminLoadingSpinner';
import AdminTableLoading, { AdminTableLoadingPresets } from '@/components/ui/AdminTableLoading';
import TripPhotosDeleteModal from '@/components/ui/TripPhotosDeleteModal';
import { PhotoAlbum } from '@/types/photo-album';
import { useToast } from '@/hooks/useToast';
import { usePhotoAlbums, useDeletePhotoAlbum, AlbumQueryParams } from '@/hooks/usePhotoAlbums';
import { handleClientError } from '@/lib/error-handler';
import { useDebounce } from '@/hooks/useDebounce';
import {
  Plus,
  Eye,
  Edit,
  Trash2,
  Camera,
  ExternalLink,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export default function TripsPhotosPage() {
  const router = useRouter();
  const toast = useToast();
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
  });
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [albumToDelete, setAlbumToDelete] = useState<PhotoAlbum | null>(null);

  // State to track failed image loads to prevent infinite retries
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());

  // Callback to handle image load errors and prevent infinite retries
  const handleImageError = useCallback((imageUrl: string) => {
    setFailedImages(prev => new Set(prev).add(imageUrl));
  }, []);

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300);

  // Use React Query for data fetching with debounced search
  const queryParams: AlbumQueryParams = {
    page: pagination.page,
    limit: pagination.limit,
    ...(debouncedSearch && { search: debouncedSearch }),
  };

  const {
    data: albumsData,
    isLoading: loading,
    error: queryError,
    refetch,
  } = usePhotoAlbums(queryParams);

  const deleteAlbumMutation = useDeletePhotoAlbum();

  const albums = albumsData?.albums || [];
  const paginationData = albumsData?.pagination || { page: 1, limit: 10, total: 0, totalPages: 1 };
  const error = queryError ? handleClientError(queryError, 'fetching albums') : null;

  // Handle search with debouncing
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on search
  };

  const handleDeleteClick = (album: PhotoAlbum) => {
    setAlbumToDelete(album);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!albumToDelete) return;

    try {
      await deleteAlbumMutation.mutateAsync(albumToDelete.id);
      setDeleteModalOpen(false);
      setAlbumToDelete(null);
      // Success and error toasts are handled by the hook
    } catch (error) {
      console.error('Error deleting album:', error);
      // Error toast is handled by the hook
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setAlbumToDelete(null);
  };

  const getAlbumStatus = (album: PhotoAlbum) => {
    if (album.oauth_user_email && album.google_photos_album_id) {
      return { status: 'ready', label: 'Ready', color: 'green' };
    } else if (album.oauth_user_email) {
      return { status: 'authorized', label: 'Authorized', color: 'blue' };
    } else {
      return { status: 'setup', label: 'Setup Required', color: 'orange' };
    }
  };

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Camera className="h-8 w-8 text-purple-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Photo Albums</h1>
              <p className="text-gray-600">Manage Google Photos albums for trips</p>
            </div>
          </div>
          <Link
            href="/admin/trips-photos/new"
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <Plus className="h-5 w-5 mr-2" />
            New Album
          </Link>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Albums
            </label>
            <input
              type="text"
              value={search}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Search by trip name..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Albums Table */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Album
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Google Photos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <AdminTableLoading
                  {...AdminTableLoadingPresets.dataTable}
                  variant="skeleton"
                />
              ) : albums.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    No photo albums found
                  </td>
                </tr>
              ) : (
                albums.map((album) => {
                  const status = getAlbumStatus(album);
                  return (
                    <tr key={album.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 mr-3 relative">
                            <Image
                              className="h-10 w-10 rounded-md object-cover"
                              src={
                                failedImages.has(album.featured_image_url || '')
                                  ? '/images/fallback-image.jpg'
                                  : album.featured_image_url || '/images/fallback-image.jpg'
                              }
                              alt={album.trip_name}
                              width={40}
                              height={40}
                              onError={() => {
                                if (album.featured_image_url && !failedImages.has(album.featured_image_url)) {
                                  handleImageError(album.featured_image_url);
                                }
                              }}
                            />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {album.trip_name}
                            </div>
                            {album.trip_description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {album.trip_description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          status.color === 'green' ? 'bg-green-100 text-green-800' :
                          status.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                          'bg-orange-100 text-orange-800'
                        }`}>
                          {status.color === 'green' && <CheckCircle className="h-3 w-3 mr-1" />}
                          {status.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {album.manual_shareable_url ? (
                          <a
                            href={album.manual_shareable_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-sm text-purple-600 hover:text-purple-800"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            View Album
                          </a>
                        ) : (
                          <span className="text-sm text-gray-400">No shareable link</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(album.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                        })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Link
                            href={`/admin/trips-photos/${album.id}`}
                            className="text-purple-600 hover:text-purple-900 p-1 rounded-full hover:bg-gray-100"
                            title="View & Upload"
                          >
                            <Eye className="h-5 w-5" />
                          </Link>
                          <Link
                            href={`/admin/trips-photos/${album.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-gray-100"
                            title="Edit Album"
                          >
                            <Edit className="h-5 w-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(album)}
                            className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-gray-100"
                            title="Delete Album"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && albums.length > 0 && (
          <div className="flex justify-between items-center mt-6">
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page === 1 || loading}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="text-sm text-gray-700">
              Page {paginationData.page} of {paginationData.totalPages} ({paginationData.total} total)
            </span>
            <button
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page === paginationData.totalPages || loading}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {albumToDelete && (
          <TripPhotosDeleteModal
            isOpen={deleteModalOpen}
            onClose={handleDeleteCancel}
            onConfirm={handleDeleteConfirm}
            album={albumToDelete}
            loading={deleteAlbumMutation.isPending}
          />
        )}
      </div>
    </AdminLayout>
  );
}
