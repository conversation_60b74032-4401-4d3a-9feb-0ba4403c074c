'use client';

import React, { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  Heart,
  Share2,
  Mountain,
  CheckCircle,
  XCircle,
  Info,
  Shield,
  Package,
  Activity,
  Train,
  Bus,
  Car,
  Plane,
  Home,
  Phone,
  CreditCard,
  AlertTriangle,
  Backpack,
  ImageIcon,
  FileText,
  DollarSign,
  Tag,
  Edit3,
  Plus,
  Trash2,
  Save,
  Upload
} from 'lucide-react';
import { TripFormData, TripItinerary, TransportMode } from '@/types/trip';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { fetchTripTemplateValues, mergeTemplateValues } from '@/lib/utils/trip-templates';
import { extractCloudinaryPublicId } from '@/lib/utils/parsing';
import DepartureDatesManager from '@/components/admin/DepartureDatesManager';
import {
  validateTripForm,
  ValidationError,
  isRequired<PERSON><PERSON>,
  getFieldError,
  hasFieldError,
  formatMissingRequiredFields,
  getValidationSummary,
  getFieldDisplayName
} from '@/lib/trip-form-validation';
import { useToast } from '@/hooks/useToast';

// Custom types for the form
interface CancellationPolicy {
  days_before: number;
  refund_percentage: number;
  description?: string;
  name?: string;
  value?: string;
}

interface TripFormProps {
  initialData?: Partial<TripFormData> & { id?: string };
  onSubmit: (data: TripFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

// Editable Text Component
const EditableText = ({ 
  value, 
  onChange, 
  className = "", 
  multiline = false, 
  placeholder = "Enter text..." 
}: {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  multiline?: boolean;
  placeholder?: string;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const handleSave = () => {
    onChange(tempValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempValue(value);
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="relative">
        {multiline ? (
          <textarea
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className={`${className} border-2 border-blue-500 rounded-lg p-2 w-full resize-none`}
            placeholder={placeholder}
            rows={multiline ? 4 : 1}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !multiline) {
                handleSave();
              } else if (e.key === 'Escape') {
                handleCancel();
              }
            }}
            autoFocus
          />
        ) : (
          <input
            type="text"
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            className={`${className} border-2 border-blue-500 rounded-lg p-2 w-full`}
            placeholder={placeholder}
            onBlur={handleSave}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSave();
              } else if (e.key === 'Escape') {
                handleCancel();
              }
            }}
            autoFocus
          />
        )}
      </div>
    );
  }

  // Determine if this is white text (for dark backgrounds)
  const isWhiteText = className.includes('text-white');
  const hoverBgClass = isWhiteText ? 'hover:bg-white/10' : 'hover:bg-blue-50';
  const editIconClass = isWhiteText ? 'text-white/70' : 'text-blue-500';

  return (
    <div
      className={`${className} cursor-pointer ${hoverBgClass} rounded-lg p-2 group relative`}
      onClick={() => setIsEditing(true)}
    >
      {value || <span className={isWhiteText ? "text-white/60" : "text-gray-400"}>{placeholder}</span>}
      <Edit3 className={`w-4 h-4 ${editIconClass} opacity-0 group-hover:opacity-100 absolute top-2 right-2`} />
    </div>
  );
};

// Editable List Component
const EditableList = ({ 
  items, 
  onChange, 
  placeholder = "Add item..." 
}: {
  items: string[] | null;
  onChange: (items: string[]) => void;
  placeholder?: string;
}) => {
  const [newItem, setNewItem] = useState('');
  const itemList = items || [];

  const addItem = () => {
    if (newItem.trim()) {
      onChange([...itemList, newItem.trim()]);
      setNewItem('');
    }
  };

  const removeItem = (index: number) => {
    onChange(itemList.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, value: string) => {
    const updated = [...itemList];
    updated[index] = value;
    onChange(updated);
  };

  return (
    <div className="space-y-3">
      {itemList.map((item, index) => (
        <div key={index} className="flex items-start gap-3 group">
          <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0 mt-2" />
          <EditableText
            value={item}
            onChange={(value) => updateItem(index, value)}
            className="flex-1"
            placeholder="Enter item..."
          />
          <button
            onClick={() => removeItem(index)}
            className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 mt-2"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      ))}
      <div className="flex items-center gap-3">
        <Plus className="w-4 h-4 text-blue-600 flex-shrink-0" />
        <input
          type="text"
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          placeholder={placeholder}
          className="flex-1 border border-gray-300 rounded-lg p-2"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              addItem();
            }
          }}
        />
        <button
          onClick={addItem}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Add
        </button>
      </div>
    </div>
  );
};

export default function TripForm({ initialData, onSubmit, onCancel, isLoading }: TripFormProps) {
  const toast = useToast();
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [uploadedImagePublicId, setUploadedImagePublicId] = useState<string | null>(null);
  const [formSubmittedSuccessfully, setFormSubmittedSuccessfully] = useState(false);

  const [formData, setFormData] = useState<TripFormData>({
    title: '',
    description: null,
    detailed_description: null,
    destination: '',
    days: 1,
    nights: 0,
    min_age: null,
    max_age: null,
    price_per_person: null,
    difficulty: 'easy',
    inclusions: null,
    exclusions: null,
    itinerary: null,
    featured_image_url: null,
    is_trek: false,
    is_active: true,
    is_featured: false,
    category: null,
    mode_of_travel: null,
    pickup_location: null,
    drop_location: null,
    property_used: null,
    activities: null,
    optional_activities: null,
    benefits: null,
    safety_supervision: null,
    things_to_carry: null,
    available_dates: null,
    payment_terms: null,
    cancellation_policy: null,
    special_notes: null,
    auto_deactivation_date: null,
    ...initialData
  });

  // Load template values for new trips
  useEffect(() => {
    let isMounted = true; // Prevent state updates if component unmounts

    const loadTemplateValues = async () => {
      // Only apply template values and default itinerary for new trips (no initialData)
      if (!initialData) {
        // Load template values
        try {
          const templateValues = await fetchTripTemplateValues();

          // Only update state if component is still mounted
          if (isMounted) {
            setFormData(prev => {
              const mergedData = mergeTemplateValues(prev, templateValues);

              // Ensure default itinerary day for new trips
              if (!mergedData.itinerary || (Array.isArray(mergedData.itinerary) && mergedData.itinerary.length === 0)) {
                mergedData.itinerary = [{
                  day: 1,
                  title: 'Day 1',
                  description: '',
                  activities: [],
                  accommodation: '',
                  transport_mode: null
                }];
              }

              return mergedData;
            });
          }
        } catch (error) {
          console.warn('Failed to load template values:', error);

          // Still ensure default itinerary day even if template loading fails
          if (isMounted) {
            setFormData(prev => ({
              ...prev,
              itinerary: prev.itinerary || [{
                day: 1,
                title: 'Day 1',
                description: '',
                activities: [],
                accommodation: '',
                transport_mode: null
              }]
            }));
          }
        }
      }
    };

    loadTemplateValues();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [initialData]); // Removed toast from dependencies

  // Helper function to ensure itinerary is an array
  const getItineraryArray = useCallback(() => {
    if (!formData.itinerary) return [];

    // If itinerary is already an array, return it
    if (Array.isArray(formData.itinerary)) {
      return formData.itinerary;
    }

    // If itinerary is an object, convert it to array
    if (typeof formData.itinerary === 'object') {
      return Object.entries(formData.itinerary).map(([day, details]: [string, any]) => ({
        day: parseInt(day),
        title: details.title || `Day ${day}`,
        subheading: details.subheading || '',
        description: details.description || '',
        activities: details.activities || [],
        accommodation: details.accommodation || '',
        transport_mode: details.transport_mode || null
      }));
    }

    return [];
  }, [formData.itinerary]);

  // Real-time validation effect
  useEffect(() => {
    const validation = validateTripForm(formData);
    setValidationErrors(validation.errors);
  }, [formData]);

  // Synchronization effect to ensure days and itinerary are always in sync
  // This effect only runs when there's a mismatch and prevents infinite loops
  useEffect(() => {
    const currentItinerary = getItineraryArray();
    const itineraryLength = currentItinerary.length;
    const daysValue = formData.days;

    // Only sync if there's a mismatch, we have valid data, and it's not during initial load
    // Also handle the case where days is 0 or negative (invalid state)
    if (daysValue !== null && daysValue !== undefined && (itineraryLength !== daysValue || daysValue <= 0)) {
      // Use a timeout to prevent rapid state updates
      const timeoutId = setTimeout(() => {
        setFormData(prev => {
          // Double-check the mismatch still exists
          const currentItineraryArray = Array.isArray(prev.itinerary) ? prev.itinerary : [];

          // Handle invalid days value (0 or negative)
          if (prev.days <= 0) {
            const correctedDays = Math.max(1, currentItineraryArray.length || 1);
            return {
              ...prev,
              days: correctedDays,
              nights: Math.max(0, correctedDays - 1)
            };
          }
          if (currentItineraryArray.length === prev.days) {
            return prev; // No change needed
          }

          let updatedItinerary = [...currentItineraryArray];

          // If days is greater than itinerary length, add missing days
          if (prev.days > currentItineraryArray.length) {
            const daysToAdd = prev.days - currentItineraryArray.length;
            const newItineraryDays = Array(daysToAdd).fill(0).map((_, index) => {
              const dayNumber = currentItineraryArray.length + index + 1;
              return {
                day: dayNumber,
                title: `Day ${dayNumber}`,
                subheading: '',
                description: '',
                activities: [],
                accommodation: '',
                transport_mode: null
              };
            });
            updatedItinerary = [...currentItineraryArray, ...newItineraryDays];
          }

          // If days is less than itinerary length, remove extra days
          if (prev.days < currentItineraryArray.length) {
            updatedItinerary = currentItineraryArray.slice(0, prev.days);
          }

          return {
            ...prev,
            itinerary: updatedItinerary,
            nights: Math.max(0, prev.days - 1) // Ensure nights is also synced
          };
        });
      }, 100); // Small delay to prevent rapid updates

      return () => clearTimeout(timeoutId);
    }
  }, [formData.days, formData.itinerary, getItineraryArray]);

  // Helper function to ensure cancellation_policy is an array
  const getCancellationPolicyArray = useCallback(() => {
    if (!formData.cancellation_policy) return [];
    
    // If cancellation_policy is already an array, return it
    if (Array.isArray(formData.cancellation_policy)) {
      return formData.cancellation_policy;
    }
    
    // If cancellation_policy is an object (from database), convert it to array
    if (typeof formData.cancellation_policy === 'object') {
      return Object.entries(formData.cancellation_policy).map(([key, value]) => ({
        name: key.replace(/_/g, ' '),  // Convert keys like "last_7_days" to "last 7 days"
        value: value as string,
        days_before: 0,  // Default values for our form structure
        refund_percentage: 0,
        description: value as string
      }));
    }
    
    // If cancellation_policy is a string (shouldn't happen, but just in case)
    if (typeof formData.cancellation_policy === 'string') {
      return [{
        name: 'policy',
        value: formData.cancellation_policy,
        days_before: 0,
        refund_percentage: 0,
        description: formData.cancellation_policy
      }];
    }
    
    return [];
  }, [formData.cancellation_policy]);

  const updateField = useCallback((field: keyof TripFormData, value: any) => {
    setFormData(prev => {
      // Special handling for days field to sync with itinerary and validate nights
      if (field === 'days') {
        // Get current itinerary directly from prev state to avoid dependency loop
        const currentItinerary = Array.isArray(prev.itinerary) ? prev.itinerary : [];
        const currentDays = prev.days;
        const newDays = value;

        // Handle null/empty days value
        if (newDays === null || newDays === undefined) {
          return {
            ...prev,
            [field]: newDays,
            nights: 0 // Set nights to 0 when days is empty
          };
        }

        // Validate days value - must be at least 1
        if (newDays <= 0) {
          console.warn('Days cannot be 0 or negative, ignoring update');
          return prev; // Don't update if days is 0 or negative
        }

        // Set nights to exactly days - 1
        const adjustedNights = Math.max(0, newDays - 1);

        let updatedItinerary = currentItinerary;

        // If days are increased, add new itinerary days
        if (newDays > currentDays) {
          const daysToAdd = newDays - currentDays;
          const newItineraryDays = Array(daysToAdd).fill(0).map((_, index) => {
            const dayNumber = currentDays + index + 1;
            return {
              day: dayNumber,
              title: `Day ${dayNumber}`,
              subheading: '',
              description: '',
              activities: [],
              accommodation: '',
              transport_mode: null
            };
          });

          updatedItinerary = [...currentItinerary, ...newItineraryDays];
        }

        // If days are decreased, remove itinerary days
        if (newDays < currentDays) {
          updatedItinerary = currentItinerary.slice(0, newDays);
        }

        return {
          ...prev,
          [field]: value,
          nights: adjustedNights,
          itinerary: updatedItinerary
        };
      }

      // Special handling for itinerary field to sync with days
      if (field === 'itinerary') {
        const newItinerary = Array.isArray(value) ? value : [];
        const newDaysCount = newItinerary.length;

        return {
          ...prev,
          [field]: value,
          days: newDaysCount,
          nights: Math.max(0, newDaysCount - 1)
        };
      }

      // Nights field is completely uneditable - it's automatically calculated
      if (field === 'nights') {
        // Ignore any attempts to manually set nights
        return prev;
      }

      // Age fields - allow all valid values, validation will show warnings but not block input
      if (field === 'min_age' || field === 'max_age') {
        return { ...prev, [field]: value };
      }

      return { ...prev, [field]: value };
    });
  }, []);

  // Function to handle adding a new itinerary day manually
  const addItineraryDay = useCallback(() => {
    const currentItinerary = getItineraryArray();
    const nextDayNumber = currentItinerary.length + 1;

    const newDay = {
      day: nextDayNumber,
      title: `Day ${nextDayNumber}`,
      subheading: '',
      description: '',
      activities: [],
      accommodation: '',
      transport_mode: null
    };

    // Update both itinerary, days count, and nights (auto-calculated)
    // Use the new itinerary length as the authoritative source for days
    const newItinerary = [...currentItinerary, newDay];
    const newDaysCount = newItinerary.length;

    setFormData(prev => ({
      ...prev,
      days: newDaysCount,
      nights: Math.max(0, newDaysCount - 1), // Auto-calculate nights
      itinerary: newItinerary
    }));
  }, [getItineraryArray]);

  // Function to handle removing an itinerary day
  const removeItineraryDay = useCallback((index: number) => {
    const currentItinerary = getItineraryArray();
    const newItinerary = [...currentItinerary];
    newItinerary.splice(index, 1);

    // Renumber the days after deletion
    const renumberedItinerary = newItinerary.map((item, idx) => ({
      ...item,
      day: idx + 1,
      title: item.title === `Day ${item.day}` ? `Day ${idx + 1}` : item.title
    }));

    // Update both itinerary, days count, and nights (auto-calculated)
    // Use the new itinerary length as the authoritative source for days
    const newDaysCount = renumberedItinerary.length;

    setFormData(prev => ({
      ...prev,
      days: newDaysCount,
      nights: Math.max(0, newDaysCount - 1), // Auto-calculate nights
      itinerary: renumberedItinerary
    }));
  }, [getItineraryArray]);

  const handleSubmit = () => {
    // Validate form before submission
    const validation = validateTripForm(formData);

    if (!validation.isValid) {
      setValidationErrors(validation.errors);

      // Show specific error message with better formatting
      if (validation.missingRequiredFields.length > 0) {
        toast.error(formatMissingRequiredFields(validation.missingRequiredFields), { duration: 8000 });
      } else {
        // Show comprehensive validation summary
        const summary = getValidationSummary(validation.errors);
        toast.error(summary, { duration: 10000 });
      }

      // Scroll to first error field
      const firstErrorField = validation.errors[0]?.field;
      if (firstErrorField) {
        const element = document.querySelector(`[name="${firstErrorField}"], [data-field="${firstErrorField}"]`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }

      return; // Don't submit if validation fails
    }

    // Clear validation errors if form is valid
    setValidationErrors([]);

    // Create a copy of the form data to submit
    const dataToSubmit = { ...formData } as any;
    
    // Handle itinerary
    if (!dataToSubmit.itinerary || !Array.isArray(dataToSubmit.itinerary)) {
      dataToSubmit.itinerary = getItineraryArray();
    }
    
    // Ensure itinerary items have all required fields
    if (dataToSubmit.itinerary) {
      dataToSubmit.itinerary = dataToSubmit.itinerary.map((day: TripItinerary) => ({
        ...day,
        activities: day.activities || [],

      }));
    }
    
    // Handle cancellation_policy - convert from array to object for database storage
    if (dataToSubmit.cancellation_policy && Array.isArray(dataToSubmit.cancellation_policy)) {
      const policyObject: Record<string, string> = {};
      
      dataToSubmit.cancellation_policy.forEach((policy: CancellationPolicy) => {
        if (policy.name && policy.value) {
          // Convert name to snake_case for database keys
          const key = policy.name.trim().toLowerCase().replace(/\s+/g, '_');
          policyObject[key] = policy.value.trim();
        }
      });
      
      // Only set if we have policies
      if (Object.keys(policyObject).length > 0) {
        dataToSubmit.cancellation_policy = policyObject;
      } else {
        dataToSubmit.cancellation_policy = null;
      }
    }
    
    // Handle other simple array fields
    const ensureArray = (value: any) => Array.isArray(value) ? value : [];
    
    dataToSubmit.inclusions = ensureArray(dataToSubmit.inclusions);
    dataToSubmit.exclusions = ensureArray(dataToSubmit.exclusions);
    dataToSubmit.activities = ensureArray(dataToSubmit.activities);
    dataToSubmit.optional_activities = ensureArray(dataToSubmit.optional_activities);
    dataToSubmit.benefits = ensureArray(dataToSubmit.benefits);
    dataToSubmit.safety_supervision = ensureArray(dataToSubmit.safety_supervision);
    dataToSubmit.things_to_carry = ensureArray(dataToSubmit.things_to_carry);
    dataToSubmit.available_dates = ensureArray(dataToSubmit.available_dates);
    dataToSubmit.special_notes = ensureArray(dataToSubmit.special_notes);
    
    // Ensure required fields have values
    dataToSubmit.min_age = dataToSubmit.min_age || null;
    dataToSubmit.max_age = dataToSubmit.max_age || null;

    try {
      onSubmit(dataToSubmit as TripFormData);
      setFormSubmittedSuccessfully(true);
      setUploadedImagePublicId(null); // Clear tracking since form was saved successfully
    } catch (error) {
      // Error handling is done by the parent component
      console.error('Form submission error:', error);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Fixed Action Bar */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-lg z-50 border-b">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {initialData ? 'Edit Trip' : 'Create New Trip'}
            </h1>

            {/* Validation Status */}
            <div className="flex items-center gap-2">
              {validationErrors.length > 0 ? (
                <div
                  className="flex items-center gap-1 text-amber-600 text-sm relative group cursor-help"
                  title={`Validation errors: ${validationErrors.map(e => e.message).join(', ')}`}
                >
                  <AlertTriangle className="h-4 w-4" />
                  {validationErrors.length} error{validationErrors.length > 1 ? 's' : ''}

                  {/* Tooltip */}
                  <div className="absolute top-full left-0 mt-2 hidden group-hover:block z-50">
                    <div className="bg-gray-900 text-white text-xs rounded-lg p-3 shadow-lg max-w-sm">
                      <div className="font-medium mb-1">Validation Errors:</div>
                      <ul className="space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index} className="flex items-start gap-1">
                            <span className="text-red-400">•</span>
                            <span>{error.message}</span>
                          </li>
                        ))}
                      </ul>
                      {/* Arrow pointing up */}
                      <div className="absolute bottom-full left-4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-green-600 text-sm">
                  <CheckCircle className="h-4 w-4" />
                  Form valid
                </div>
              )}


            </div>
          </div>

          <div className="flex gap-4">
            {onCancel && (
              <button
                onClick={async () => {
                  // Only cleanup uploaded image if form is being cancelled (not saved) and it's a new trip
                  if (uploadedImagePublicId && !initialData?.id && !formSubmittedSuccessfully && formData.featured_image_url?.includes('cloudinary.com')) {
                    try {
                      await fetch('/api/admin/cloudinary/cleanup-single', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                          publicId: uploadedImagePublicId,
                          context: 'form-cancelled',
                          shouldCleanupFolder: true
                        })
                      });
                      console.log(`✅ Cleaned up cancelled trip image: ${uploadedImagePublicId}`);
                    } catch (error) {
                      console.error(`❌ Failed to cleanup cancelled trip image: ${uploadedImagePublicId}`, error);
                    }
                  }
                  onCancel();
                }}
                className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={isLoading}
              >
                Cancel
              </button>
            )}
            <button
              onClick={handleSubmit}
              disabled={isLoading || validationErrors.length > 0}
              className={`px-6 py-2 rounded-lg flex items-center gap-2 disabled:opacity-50 ${
                validationErrors.length > 0
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-green-600 text-white hover:from-blue-700 hover:to-green-700'
              }`}
            >
              <Save className="w-4 h-4" />
              {isLoading ? 'Saving...' : 'Save Trip'}
            </button>
          </div>
        </div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="pt-20"
      >
        {/* Hero Section */}
        <motion.section variants={itemVariants} className="relative h-[70vh] overflow-hidden">
          <div className="relative w-full h-full">
            <Image
              src={formData.featured_image_url || '/images/fallback-trip.jpg'}
              alt={formData.title || 'Trip Image'}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
            
            {/* Image Upload Button */}
            <div className="absolute top-6 right-6 z-10">
              <button
                onClick={() => setShowImageUpload(true)}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 font-medium transition-all duration-200 ${
                  formData.featured_image_url
                    ? 'bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                }`}
              >
                <Upload className="w-4 h-4" />
                {formData.featured_image_url ? 'Change Image' : 'Upload Image'}
              </button>
            </div>

            {/* Hero Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
              <div className="max-w-7xl mx-auto">
                <div className="flex flex-wrap items-end justify-between gap-6">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="px-3 py-1 bg-blue-600 rounded-full">
                        <span className="text-sm font-medium text-white bg-transparent border-none p-0">
                          {formData.destination}
                        </span>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <EditableText
                        value={formData.title}
                        onChange={(value) => updateField('title', value)}
                        className={`text-4xl md:text-6xl font-bold mb-2 text-white bg-transparent border-none p-0 ${
                          hasFieldError(validationErrors, 'title') ? 'border-red-500' : ''
                        }`}
                        placeholder="Trip Title *"
                      />
                      {hasFieldError(validationErrors, 'title') && (
                        <div className="absolute -bottom-6 left-0 flex items-center gap-1 text-red-300 text-sm">
                          <AlertTriangle className="h-4 w-4" />
                          {getFieldError(validationErrors, 'title')}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap items-center gap-6 text-white/80 mt-4">
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{formData.days}</span>
                        <span>Days</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        <span>{formData.nights}</span>
                        <span>Nights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        <span>Educational Tour</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mountain className="w-5 h-5" />
                        <span className="capitalize">{formData.difficulty}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-3xl font-bold mb-2">
                      {formData.price_per_person ? `₹${formData.price_per_person.toLocaleString()}` : '₹--'}
                    </div>
                    <div className="text-white/80 mb-4">per person</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Left Column - Main Content - Scrolls with page */}
            <div className="lg:col-span-2 space-y-12 lg:pr-4">
              {/* Trip Overview */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">
                  Trip Overview
                </h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-lg font-medium text-gray-900 mb-3">
                      Trip Overview Description
                    </label>
                    <div className="relative">
                      <EditableText
                        value={formData.description || ''}
                        onChange={(value) => updateField('description', value)}
                        className={`text-gray-700 text-lg leading-relaxed mb-8 ${
                          hasFieldError(validationErrors, 'description') ? 'border-red-300' : ''
                        }`}
                        multiline
                        placeholder="Enter a brief overview of the trip (10-500 characters)..."
                      />
                      {hasFieldError(validationErrors, 'description') && (
                        <div className="flex items-center gap-1 text-red-600 text-sm mt-2">
                          <AlertTriangle className="h-4 w-4" />
                          {getFieldError(validationErrors, 'description')}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Quick Info Grid */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Clock className="w-5 h-5 text-blue-600" />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          Duration
                          <span className="text-red-500 ml-1">*</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={formData.days?.toString() || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              // Allow empty string or valid positive numbers
                              if (value === '') {
                                updateField('days', null);
                              } else if (/^\d+$/.test(value)) {
                                const numericValue = parseInt(value, 10);
                                // Ensure days is at least 1 and at most 30
                                if (numericValue >= 1 && numericValue <= 30) {
                                  updateField('days', numericValue);
                                }
                                // Reject 0 or negative values silently (don't update)
                              }
                            }}
                            onBlur={(e) => {
                              // Only set default value on blur if field is empty
                              const value = e.target.value;
                              if (value === '' || parseInt(value) < 1) {
                                updateField('days', 1);
                              }
                            }}
                            className={`w-16 border rounded px-2 py-1 text-center ${
                              hasFieldError(validationErrors, 'days')
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                : 'border-gray-300'
                            }`}
                            inputMode="numeric"
                            placeholder="1"
                          />
                          <span>Days,</span>
                          <div className="w-16 bg-gray-100 border border-gray-300 rounded px-2 py-1 text-center text-gray-600 font-medium">
                            {formData.nights}
                          </div>
                          <span>Nights (auto)</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Nights are automatically calculated as days - 1
                        </div>
                        {hasFieldError(validationErrors, 'days') && (
                          <div className="flex items-center gap-1 text-red-600 text-xs mt-1">
                            <AlertTriangle className="h-3 w-3" />
                            {getFieldError(validationErrors, 'days')}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-900">Age Range</div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <input
                            type="text"
                            value={formData.min_age || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (value === '') {
                                updateField('min_age', null);
                              } else if (/^\d+$/.test(value)) {
                                const numericValue = parseInt(value, 10);
                                if (numericValue >= 1 && numericValue <= 100) {
                                  updateField('min_age', numericValue);
                                }
                              }
                            }}
                            className={`w-16 border rounded px-2 py-1 text-center ${
                              hasFieldError(validationErrors, 'min_age')
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                : 'border-gray-300'
                            }`}
                            placeholder="Min"
                            inputMode="numeric"
                          />
                          -
                          <input
                            type="text"
                            value={formData.max_age || ''}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (value === '') {
                                updateField('max_age', null);
                              } else if (/^\d+$/.test(value)) {
                                const numericValue = parseInt(value, 10);
                                if (numericValue >= 1 && numericValue <= 100) {
                                  updateField('max_age', numericValue);
                                }
                              }
                            }}
                            className={`w-16 border rounded px-2 py-1 text-center ${
                              hasFieldError(validationErrors, 'max_age')
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                : 'border-gray-300'
                            }`}
                            placeholder="Max"
                            inputMode="numeric"
                          />
                          <span>years</span>
                        </div>
                        {(hasFieldError(validationErrors, 'min_age') || hasFieldError(validationErrors, 'max_age')) && (
                          <div className="text-xs text-red-500 mt-1">
                            {hasFieldError(validationErrors, 'min_age') && (
                              <div>{getFieldError(validationErrors, 'min_age')}</div>
                            )}
                            {hasFieldError(validationErrors, 'max_age') && (
                              <div>{getFieldError(validationErrors, 'max_age')}</div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Mountain className="w-5 h-5 text-blue-600" />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          Difficulty
                          <span className="text-red-500 ml-1">*</span>
                        </div>
                        <div>
                          <select
                            value={formData.difficulty}
                            onChange={(e) => updateField('difficulty', e.target.value as any)}
                            className={`border rounded px-2 py-1 capitalize ${
                              hasFieldError(validationErrors, 'difficulty')
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                : 'border-gray-300'
                            }`}
                          >
                            <option value="easy">Easy</option>
                            <option value="moderate">Moderate</option>
                            <option value="challenging">Challenging</option>
                            <option value="difficult">Difficult</option>
                          </select>
                          {hasFieldError(validationErrors, 'difficulty') && (
                            <div className="flex items-center gap-1 text-red-600 text-xs mt-1">
                              <AlertTriangle className="h-3 w-3" />
                              {getFieldError(validationErrors, 'difficulty')}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-blue-600" />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          Destination
                          <span className="text-red-500 ml-1">*</span>
                        </div>
                        <div>
                          <input
                            type="text"
                            value={formData.destination}
                            onChange={(e) => updateField('destination', e.target.value)}
                            className={`border rounded px-2 py-1 w-full ${
                              hasFieldError(validationErrors, 'destination')
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                : 'border-gray-300'
                            }`}
                            placeholder="Enter destination *"
                          />
                          {hasFieldError(validationErrors, 'destination') && (
                            <div className="flex items-center gap-1 text-red-600 text-xs mt-1">
                              <AlertTriangle className="h-3 w-3" />
                              {getFieldError(validationErrors, 'destination')}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Trip Details */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">About This Trip</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-lg font-medium text-gray-900 mb-3">
                      Detailed Description
                    </label>
                    <div className="relative">
                      <EditableText
                        value={formData.detailed_description || ''}
                        onChange={(value) => updateField('detailed_description', value)}
                        className={`text-gray-700 text-lg leading-relaxed ${
                          hasFieldError(validationErrors, 'detailed_description') ? 'border-red-300' : ''
                        }`}
                        multiline
                        placeholder="Enter detailed description about the trip, activities, highlights, and what makes this trip special (10-5000 characters)..."
                      />
                      {hasFieldError(validationErrors, 'detailed_description') && (
                        <div className="flex items-center gap-1 text-red-600 text-sm mt-2">
                          <AlertTriangle className="h-4 w-4" />
                          {getFieldError(validationErrors, 'detailed_description')}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Itinerary */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Trip Itinerary</h2>
                <div className="space-y-6">
                  {(getItineraryArray() || []).map((day, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 relative">
                      <button
                        onClick={() => removeItineraryDay(index)}
                        className="absolute top-2 right-2 text-red-500"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Day
                          </label>
                          <input
                            type="number"
                            value={index + 1} // Auto-number based on index
                            readOnly
                            className="w-full border border-gray-200 rounded p-2 bg-gray-50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Title
                          </label>
                          <input
                            type="text"
                            value={day.title}
                            onChange={(e) => {
                              const newItinerary = [...getItineraryArray()];
                              newItinerary[index] = {
                                ...newItinerary[index],
                                title: e.target.value,
                                day: index + 1 // Ensure day number matches index+1
                              };
                              updateField('itinerary', newItinerary);
                            }}
                            className="w-full border border-gray-300 rounded p-2"
                          />
                        </div>
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Subheading
                        </label>
                        <input
                          type="text"
                          value={day.subheading || ''}
                          onChange={(e) => {
                            const newItinerary = [...getItineraryArray()];
                            newItinerary[index] = {
                              ...newItinerary[index],
                              subheading: e.target.value
                            };
                            updateField('itinerary', newItinerary);
                          }}
                          className="w-full border border-gray-300 rounded p-2"
                          placeholder="Brief description for collapsed view..."
                        />
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={day.description}
                          onChange={(e) => {
                            const newItinerary = [...getItineraryArray()];
                            newItinerary[index] = {
                              ...newItinerary[index],
                              description: e.target.value
                            };
                            updateField('itinerary', newItinerary);
                          }}
                          rows={3}
                          className="w-full border border-gray-300 rounded p-2"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Accommodation
                          </label>
                          <input
                            type="text"
                            value={day.accommodation || ''}
                            onChange={(e) => {
                              const newItinerary = [...getItineraryArray()];
                              newItinerary[index] = {
                                ...newItinerary[index],
                                accommodation: e.target.value
                              };
                              updateField('itinerary', newItinerary);
                            }}
                            className="w-full border border-gray-300 rounded p-2"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Transport Mode
                          </label>
                          <select
                            value={day.transport_mode || ''}
                            onChange={(e) => {
                              const newItinerary = [...getItineraryArray()];
                              newItinerary[index] = {
                                ...newItinerary[index],
                                transport_mode: e.target.value as TransportMode || null
                              };
                              updateField('itinerary', newItinerary);
                            }}
                            className="w-full border border-gray-300 rounded p-2"
                          >
                            <option value="">Select transport mode</option>
                            <option value="train">🚂 Train</option>
                            <option value="bus">🚌 Bus</option>
                            <option value="car">🚗 Car</option>
                            <option value="flight">✈️ Flight</option>
                          </select>
                        </div>
                      </div>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Activities
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {(day.activities || []).map((activity: string, actIndex: number) => (
                            <div key={actIndex} className="flex items-center bg-blue-50 rounded-full px-3 py-1">
                              <span>{activity}</span>
                              <button
                                onClick={() => {
                                  const newItinerary = [...getItineraryArray()];
                                  const newActivities = [...(newItinerary[index].activities || [])];
                                  newActivities.splice(actIndex, 1);
                                  newItinerary[index] = {
                                    ...newItinerary[index],
                                    activities: newActivities
                                  };
                                  updateField('itinerary', newItinerary);
                                }}
                                className="ml-2 text-red-500"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                          <form
                            onSubmit={(e) => {
                              e.preventDefault();
                              const input = e.currentTarget.elements.namedItem('newActivity') as HTMLInputElement;
                              if (input.value.trim()) {
                                const newItinerary = [...getItineraryArray()];
                                const newActivities = [...(newItinerary[index].activities || []), input.value.trim()];
                                newItinerary[index] = {
                                  ...newItinerary[index],
                                  activities: newActivities
                                };
                                updateField('itinerary', newItinerary);
                                input.value = '';
                              }
                            }}
                            className="flex"
                          >
                            <input
                              type="text"
                              name="newActivity"
                              placeholder="Add activity..."
                              className="border border-gray-300 rounded-l p-1 text-sm"
                            />
                            <button
                              type="submit"
                              className="bg-blue-500 text-white rounded-r p-1 text-sm"
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </form>
                        </div>
                      </div>

                    </div>
                  ))}
                  <button
                    onClick={addItineraryDay}
                    className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:border-gray-400"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Add Itinerary Day
                  </button>
                </div>
              </motion.section>

              {/* Travel Information */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Travel Information</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="flex items-start gap-3">
                    <Train className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Mode of Travel</div>
                      <EditableText
                        value={formData.mode_of_travel || ''}
                        onChange={(value) => updateField('mode_of_travel', value)}
                        className="text-gray-600"
                        placeholder="Enter mode of travel..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Pickup Location</div>
                      <EditableText
                        value={formData.pickup_location || ''}
                        onChange={(value) => updateField('pickup_location', value)}
                        className="text-gray-600"
                        placeholder="Enter pickup location..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-red-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Drop Location</div>
                      <EditableText
                        value={formData.drop_location || ''}
                        onChange={(value) => updateField('drop_location', value)}
                        className="text-gray-600"
                        placeholder="Enter drop location..."
                      />
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Home className="w-5 h-5 text-purple-600 mt-1 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 mb-1">Accommodation</div>
                      <EditableText
                        value={formData.property_used || ''}
                        onChange={(value) => updateField('property_used', value)}
                        className="text-gray-600"
                        placeholder="Enter accommodation details..."
                      />
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Activities */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Activities</h2>
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Activities Included</h3>
                  <EditableList
                    items={formData.activities}
                    onChange={(items) => updateField('activities', items)}
                    placeholder="Add activity..."
                  />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-gray-900">Optional Activities</h3>
                  <EditableList
                    items={formData.optional_activities}
                    onChange={(items) => updateField('optional_activities', items)}
                    placeholder="Add optional activity..."
                  />
                </div>
              </motion.section>

              {/* Inclusions & Exclusions */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">What's Included & Excluded</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Inclusions */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 flex-shrink-0" />
                      Included
                    </h3>
                    <EditableList
                      items={formData.inclusions}
                      onChange={(items) => updateField('inclusions', items)}
                      placeholder="Add inclusion..."
                    />
                  </div>

                  {/* Exclusions */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-red-700 flex items-center gap-2">
                      <XCircle className="w-5 h-5 flex-shrink-0" />
                      Not Included
                    </h3>
                    <EditableList
                      items={formData.exclusions}
                      onChange={(items) => updateField('exclusions', items)}
                      placeholder="Add exclusion..."
                    />
                  </div>
                </div>
              </motion.section>

              {/* Safety & Benefits */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900">Safety & Benefits</h2>
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Benefits */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-blue-700 flex items-center gap-2">
                      <Package className="w-5 h-5 flex-shrink-0" />
                      Trip Benefits
                    </h3>
                    <EditableList
                      items={formData.benefits}
                      onChange={(items) => updateField('benefits', items)}
                      placeholder="Add benefit..."
                    />
                  </div>

                  {/* Safety */}
                  <div>
                    <h3 className="text-xl font-semibold mb-4 text-green-700 flex items-center gap-2">
                      <Shield className="w-5 h-5 flex-shrink-0" />
                      Safety & Supervision
                    </h3>
                    <EditableList
                      items={formData.safety_supervision}
                      onChange={(items) => updateField('safety_supervision', items)}
                      placeholder="Add safety measure..."
                    />
                  </div>
                </div>
              </motion.section>

              {/* Things to Carry */}
              <motion.section variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-3xl font-bold mb-6 text-gray-900 flex items-center gap-3">
                  <Backpack className="w-8 h-8 text-orange-600 flex-shrink-0" />
                  Things to Carry
                </h2>
                <EditableList
                  items={formData.things_to_carry}
                  onChange={(items) => updateField('things_to_carry', items)}
                  placeholder="Add item to carry..."
                />
              </motion.section>

              {/* Special Notes */}
              <motion.section variants={itemVariants} className="bg-yellow-50 border border-yellow-200 rounded-2xl p-8">
                <h2 className="text-2xl font-bold mb-6 text-yellow-800 flex items-center gap-3">
                  <Info className="w-6 h-6 flex-shrink-0" />
                  Important Notes
                </h2>
                <EditableList
                  items={formData.special_notes}
                  onChange={(items) => updateField('special_notes', items)}
                  placeholder="Add important note..."
                />
              </motion.section>

              {/* Payment Terms */}
              <motion.section variants={itemVariants} className="bg-blue-50 border border-blue-200 rounded-2xl p-8">
                <h2 className="text-2xl font-bold mb-6 text-blue-800 flex items-center gap-3">
                  <DollarSign className="w-6 h-6" />
                  Payment Terms
                </h2>
                <EditableText
                  value={formData.payment_terms || ''}
                  onChange={(value) => updateField('payment_terms', value)}
                  className="text-blue-800 leading-relaxed"
                  multiline
                  placeholder="Enter payment terms..."
                />
              </motion.section>
            </div>

            {/* Right Column - Settings Sidebar - Sticky and independently scrollable */}
            <div className="lg:col-span-1 lg:pl-4">
              <div className="sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto">
                <motion.div variants={itemVariants} className="space-y-6">
                {/* Pricing Card */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="text-xl font-semibold mb-4">Pricing & Settings</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price per Person (₹)
                        <span className="text-red-500 ml-1">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.price_per_person || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Only allow digits or empty string
                          if (value === '' || /^\d+$/.test(value)) {
                            const numericValue = value === '' ? null : parseInt(value, 10);
                            updateField('price_per_person', numericValue);
                          }
                        }}
                        className={`w-full border rounded-lg p-2 ${
                          hasFieldError(validationErrors, 'price_per_person')
                            ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                            : 'border-gray-300'
                        }`}
                        placeholder="Enter price per person *"
                        inputMode="numeric"
                      />
                      {hasFieldError(validationErrors, 'price_per_person') && (
                        <div className="flex items-center gap-1 text-red-600 text-sm mt-1">
                          <AlertTriangle className="h-4 w-4" />
                          {getFieldError(validationErrors, 'price_per_person')}
                        </div>
                      )}
                    </div>



                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category
                      </label>
                      <input
                        type="text"
                        value={formData.category || ''}
                        onChange={(e) => updateField('category', e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2"
                        placeholder="e.g., Adventure, Cultural"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Auto Deactivation Date (IST)
                      </label>
                      <input
                        type="datetime-local"
                        value={formData.auto_deactivation_date ? (() => {
                          // Convert UTC date from database to IST for display
                          const utcDate = new Date(formData.auto_deactivation_date);
                          const istDate = new Date(utcDate.getTime() + (5.5 * 60 * 60 * 1000));
                          // Format for datetime-local input (YYYY-MM-DDTHH:MM)
                          return istDate.toISOString().slice(0, 16);
                        })() : ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value) {
                            // Create IST datetime string and let PostgreSQL handle timezone conversion
                            // PostgreSQL will automatically convert this to UTC for storage
                            const istDateTimeString = value + ':00+05:30'; // Add seconds and IST timezone offset
                            updateField('auto_deactivation_date', istDateTimeString);
                          } else {
                            updateField('auto_deactivation_date', null);
                          }
                        }}
                        className="w-full border border-gray-300 rounded-lg p-2"
                        min={(() => {
                          // Set minimum to current time in IST
                          const now = new Date();
                          const istNow = new Date(now.getTime() + (5.5 * 60 * 60 * 1000));
                          return istNow.toISOString().slice(0, 16);
                        })()}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Trip will be automatically deactivated at this date/time (Indian Standard Time)
                      </p>
                      {formData.auto_deactivation_date && (
                        <div className="mt-2 space-y-1">
                          <p className="text-xs text-blue-600">
                            <strong>IST:</strong> {new Date(formData.auto_deactivation_date).toLocaleString('en-IN', {
                              timeZone: 'Asia/Kolkata',
                              dateStyle: 'full',
                              timeStyle: 'short'
                            })}
                          </p>
                          <p className="text-xs text-gray-500">
                            <strong>UTC:</strong> {new Date(formData.auto_deactivation_date).toLocaleString('en-IN', {
                              timeZone: 'UTC',
                              dateStyle: 'full',
                              timeStyle: 'short'
                            })}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={formData.is_trek === true}
                          onChange={(e) => updateField('is_trek', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-gray-700">Is Trek</span>
                      </label>
                    </div>


                  </div>
                </div>

                {/* Departure Dates */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mt-6">
                  <DepartureDatesManager
                    dates={formData.available_dates}
                    onChange={(dates) => updateField('available_dates', dates)}
                  />
                </div>

                {/* Cancellation Policy */}
                <div className="bg-white rounded-2xl p-6 shadow-lg mt-6">
                  <h3 className="text-xl font-semibold mb-4">Cancellation Policy</h3>
                  <div className="space-y-4">
                    {(getCancellationPolicyArray() || []).map((policy, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 relative">
                        <button
                          onClick={() => {
                            const policyArray = [...getCancellationPolicyArray()];
                            policyArray.splice(index, 1);
                            updateField('cancellation_policy', policyArray);
                          }}
                          className="absolute top-2 right-2 text-red-500"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Policy Name
                            </label>
                            <input
                              type="text"
                              value={policy.name || ''}
                              onChange={(e) => {
                                const policyArray = [...getCancellationPolicyArray()];
                                policyArray[index] = {
                                  ...policyArray[index],
                                  name: e.target.value
                                };
                                updateField('cancellation_policy', policyArray);
                              }}
                              className="w-full border border-gray-300 rounded p-2"
                              placeholder="e.g., Minimum, Last 7 days"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Refund Value
                            </label>
                            <input
                              type="text"
                              value={policy.value || ''}
                              onChange={(e) => {
                                const policyArray = [...getCancellationPolicyArray()];
                                policyArray[index] = {
                                  ...policyArray[index],
                                  value: e.target.value
                                };
                                updateField('cancellation_policy', policyArray);
                              }}
                              className="w-full border border-gray-300 rounded p-2"
                              placeholder="e.g., 25%, No Refund"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                    <button
                      onClick={() => {
                        const newPolicy = { name: '', value: '', days_before: 0, refund_percentage: 0, description: '' };
                        updateField('cancellation_policy', [...getCancellationPolicyArray(), newPolicy]);
                      }}
                      className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:border-gray-400"
                    >
                      <Plus className="w-5 h-5 mr-2" />
                      Add Cancellation Policy
                    </button>
                  </div>
                </div>
              </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Image Upload Modal */}
      {showImageUpload && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Upload Trip Featured Image
              </h3>
              <CloudinaryUpload
                onUpload={(url) => {
                  updateField('featured_image_url', url);
                  setShowImageUpload(false);
                  // Track uploaded image for potential cleanup using robust parsing
                  const publicId = extractCloudinaryPublicId(url);
                  setUploadedImagePublicId(publicId);
                }}
                currentImage={formData.featured_image_url || undefined}
                uploadType="trip"
                entityId={initialData?.id || 'new'}
                entityName={formData.title || 'untitled-trip'}
                placeholder="Upload trip featured image"
                className="h-80"
              />
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowImageUpload(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}